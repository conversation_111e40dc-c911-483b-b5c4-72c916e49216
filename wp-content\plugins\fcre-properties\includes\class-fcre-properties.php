<?php

/**
 * The file that defines the core plugin class
 *
 * A class definition that includes attributes and functions used across both the
 * public-facing side of the site and the admin area.
 *
 * @link       https://focusedcre.com
 * @since      1.0.0
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/includes
 */

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * Also maintains the unique identifier of this plugin as well as the current
 * version of the plugin.
 *
 * @since      1.0.0
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/includes
 * <AUTHOR> CRE <<EMAIL>>
 */
class Fcre_Properties {

	/**
	 * The loader that's responsible for maintaining and registering all hooks that power
	 * the plugin.
	 *
	 * @since    1.0.0
	 * @access   protected
	 * @var      Fcre_Properties_Loader    $loader    Maintains and registers all hooks for the plugin.
	 */
	protected $loader;

	/**
	 * The unique identifier of this plugin.
	 *
	 * @since    1.0.0
	 * @access   protected
	 * @var      string    $plugin_name    The string used to uniquely identify this plugin.
	 */
	protected $plugin_name;

	/**
	 * The current version of the plugin.
	 *
	 * @since    1.0.0
	 * @access   protected
	 * @var      string    $version    The current version of the plugin.
	 */
	protected $version;

	/**
	 * Define the core functionality of the plugin.
	 *
	 * Set the plugin name and the plugin version that can be used throughout the plugin.
	 * Load the dependencies, define the locale, and set the hooks for the admin area and
	 * the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function __construct() {
		if ( defined( 'FCRE_PROPERTIES_VERSION' ) ) {
			$this->version = FCRE_PROPERTIES_VERSION;
		} else {
			$this->version = '1.0.0';
		}
		$this->plugin_name = 'fcre-properties';

		$this->load_dependencies();
		$this->set_locale();
		$this->define_admin_hooks();
		$this->define_public_hooks();

	}

	/**
	 * Load the required dependencies for this plugin.
	 *
	 * Include the following files that make up the plugin:
	 *
	 * - Fcre_Properties_Loader. Orchestrates the hooks of the plugin.
	 * - Fcre_Properties_i18n. Defines internationalization functionality.
	 * - Fcre_Properties_Admin. Defines all hooks for the admin area.
	 * - Fcre_Properties_Public. Defines all hooks for the public side of the site.
	 *
	 * Create an instance of the loader which will be used to register the hooks
	 * with WordPress.
	 *
	 * @since    1.0.0
	 * @access   private
	 */
	private function load_dependencies() {

		/**
		 * The class responsible for orchestrating the actions and filters of the
		 * core plugin.
		 */
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-fcre-properties-loader.php';

		/**
		 * The class responsible for defining internationalization functionality
		 * of the plugin.
		 */
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'includes/class-fcre-properties-i18n.php';

		require plugin_dir_path( dirname( __FILE__ ) ) . 'includes/fcre-functions.php';

		/**
		 * The class responsible for defining all actions that occur in the admin area.
		 */
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/class-fcre-properties-admin.php';

		/**
		 * The class responsible for registering all custom post types.
		 */

		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/class-fcre-cpt.php';

		/**
		 * This class is used to add metaboxes to the custom post types.
		 */
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/class-fcre-metabox.php';

		/**
		 * The class responsible for defining all actions that occur in the public-facing
		 * side of the site.
		 */
		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'public/class-fcre-properties-public.php';

		require_once plugin_dir_path( dirname( __FILE__ ) ) . 'admin/partials/metabox/fields/dynamic-fields.php';


		$this->loader = new Fcre_Properties_Loader();

	}

	/**
	 * Define the locale for this plugin for internationalization.
	 *
	 * Uses the Fcre_Properties_i18n class in order to set the domain and to register the hook
	 * with WordPress.
	 *
	 * @since    1.0.0
	 * @access   private
	 */
	private function set_locale() {

		$plugin_i18n = new Fcre_Properties_i18n();

		$this->loader->add_action( 'plugins_loaded', $plugin_i18n, 'load_plugin_textdomain' );

	}

	/**
	 * Register all of the hooks related to the admin area functionality
	 * of the plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 */
	private function define_admin_hooks() {

		$plugin_admin = new Fcre_Properties_Admin();

		$this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_styles' );
		$this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts' );
		$this->loader->add_action( 'admin_menu', $plugin_admin, 'fcre_plugin_menu' );
		$this->loader->add_action( 'wp_ajax_fcre_filter_properties', $plugin_admin, 'fcre_filter_properties' );
		$this->loader->add_action( 'wp_ajax_nopriv_fcre_filter_properties', $plugin_admin, 'fcre_filter_properties' );
		$this->loader->add_action( 'wp_ajax_fcre_submit_agreement', $plugin_admin, 'submit_agreement' );
		$this->loader->add_action( 'wp_ajax_nopriv_fcre_submit_agreement', $plugin_admin, 'submit_agreement' );
		$this->loader->add_action( 'wp_ajax_fcre_submit_request_more_info', $plugin_admin, 'submit_request_more_info' );
		$this->loader->add_action( 'wp_ajax_nopriv_fcre_submit_request_more_info', $plugin_admin, 'submit_request_more_info' );
		$this->loader->add_action( 'wp_ajax_fcre_check_signed_agreement', $plugin_admin, 'check_signed_agreement' );
		$this->loader->add_action( 'wp_ajax_nopriv_fcre_check_signed_agreement', $plugin_admin, 'check_signed_agreement' );
		 

		$plugin_cpt = new Fcre_CPT();

		$this->loader->add_action( 'init', $plugin_cpt, 'fcre_init_properties_custom_post' );
        $this->loader->add_action( 'init', $plugin_cpt, 'fcre_init_agents_custom_post' );
        $this->loader->add_action( 'init', $plugin_cpt, 'fcre_init_agreement_custom_post' );

		$plugin_metabox = new Fcre_Metabox();

		$this->loader->add_action( 'add_meta_boxes', $plugin_metabox, 'snapcre_add_custom_box' );
		$this->loader->add_action( 'save_post', $plugin_metabox, 'fcre_save_properties_metabox' );
		$this->loader->add_action( 'add_meta_boxes', $plugin_metabox, 'fcre_agent_add_custom_box' );
		$this->loader->add_action( 'save_post', $plugin_metabox, 'fcre_save_agent_metabox' );

	}

	/**
	 * Register all of the hooks related to the public-facing functionality
	 * of the plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 */
	private function define_public_hooks() {

		$plugin_public = new Fcre_Properties_Public();

		$this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_styles' );
		$this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_scripts' );
		$this->loader->add_action( 'wp_head', $plugin_public, 'add_custom_colors_css' );
		$this->loader->add_filter( 'single_template', $plugin_public, 'fcre_single_post_template' );

		 //short code [snapcre_properties] , [snapcre_properties property_types="3, 1" , transaction_types="1"]

		$this->loader->add_shortcode( 'snapcre_properties', $plugin_public, 'snapcre_filters' );

	}

	/**
	 * Run the loader to execute all of the hooks with WordPress.
	 *
	 * @since    1.0.0
	 */
	public function run() {
		$this->loader->run();
	}

	/**
	 * The name of the plugin used to uniquely identify it within the context of
	 * WordPress and to define internationalization functionality.
	 *
	 * @since     1.0.0
	 * @return    string    The name of the plugin.
	 */
	public function get_plugin_name() {
		return $this->plugin_name;
	}

	/**
	 * The reference to the class that orchestrates the hooks with the plugin.
	 *
	 * @since     1.0.0
	 * @return    Fcre_Properties_Loader    Orchestrates the hooks of the plugin.
	 */
	public function get_loader() {
		return $this->loader;
	}

	/**
	 * Retrieve the version number of the plugin.
	 *
	 * @since     1.0.0
	 * @return    string    The version number of the plugin.
	 */
	public function get_version() {
		return $this->version;
	}

}
