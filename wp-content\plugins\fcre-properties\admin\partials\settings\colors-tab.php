<?php
if (!defined('ABSPATH')) {
    die('-1');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Save color settings
    if (isset($_POST[$this->FCRE->plugin_name . '-primary-color'])) {
        update_option($this->FCRE->plugin_name . '-primary-color', sanitize_hex_color($_POST[$this->FCRE->plugin_name . '-primary-color']));
    }
    if (isset($_POST[$this->FCRE->plugin_name . '-secondary-color'])) {
        update_option($this->FCRE->plugin_name . '-secondary-color', sanitize_hex_color($_POST[$this->FCRE->plugin_name . '-secondary-color']));
    }
    if (isset($_POST[$this->FCRE->plugin_name . '-button-color'])) {
        update_option($this->FCRE->plugin_name . '-button-color', sanitize_hex_color($_POST[$this->FCRE->plugin_name . '-button-color']));
    }
    if (isset($_POST[$this->FCRE->plugin_name . '-button-hover-color'])) {
        update_option($this->FCRE->plugin_name . '-button-hover-color', sanitize_hex_color($_POST[$this->FCRE->plugin_name . '-button-hover-color']));
    }
    
    // Show success message
    echo '<div class="notice notice-success is-dismissible"><p>Color settings saved successfully!</p></div>';
}

// Get saved color values
$primary_color = get_option($this->FCRE->plugin_name . '-primary-color', '#007cba');
$secondary_color = get_option($this->FCRE->plugin_name . '-secondary-color', '#005a87');
$button_color = get_option($this->FCRE->plugin_name . '-button-color', '#0073aa');
$button_hover_color = get_option($this->FCRE->plugin_name . '-button-hover-color', '#005a87');
?>

<div class="tab-content-header">
    <h2>Color Settings</h2>
    <p>Customize the colors for your property listings and frontend elements.</p>
</div>

<table class="fcre-color-picker-table">
    <tr>
        <td colspan="2">
            <p>Choose colors that match your brand and website design. These colors will be applied to various frontend elements including headers, buttons, and property listing components.</p>
        </td>
    </tr>
    
    <tr>
        <td valign="top" width="25%">
            <h3>Primary Color</h3>
            <small>Main brand color used for headers and primary elements</small>
        </td>
        <td>
            <input type="color" 
                   name="<?= $this->FCRE->plugin_name ?>-primary-color" 
                   value="<?php echo esc_attr($primary_color); ?>" 
                   class="fcre-color-picker" 
                   id="primary-color">
            <input type="text" 
                   value="<?php echo esc_attr($primary_color); ?>" 
                   class="fcre-color-text" 
                   data-target="primary-color"
                   placeholder="#007cba">
        </td>
    </tr>
    
    <tr>
        <td valign="top" width="25%">
            <h3>Secondary Color</h3>
            <small>Secondary brand color for accents and highlights</small>
        </td>
        <td>
            <input type="color" 
                   name="<?= $this->FCRE->plugin_name ?>-secondary-color" 
                   value="<?php echo esc_attr($secondary_color); ?>" 
                   class="fcre-color-picker" 
                   id="secondary-color">
            <input type="text" 
                   value="<?php echo esc_attr($secondary_color); ?>" 
                   class="fcre-color-text" 
                   data-target="secondary-color"
                   placeholder="#005a87">
        </td>
    </tr>
    
    <tr>
        <td valign="top" width="25%">
            <h3>Button Color</h3>
            <small>Default color for buttons and call-to-action elements</small>
        </td>
        <td>
            <input type="color" 
                   name="<?= $this->FCRE->plugin_name ?>-button-color" 
                   value="<?php echo esc_attr($button_color); ?>" 
                   class="fcre-color-picker" 
                   id="button-color">
            <input type="text" 
                   value="<?php echo esc_attr($button_color); ?>" 
                   class="fcre-color-text" 
                   data-target="button-color"
                   placeholder="#0073aa">
        </td>
    </tr>
    
    <tr>
        <td valign="top" width="25%">
            <h3>Button Hover Color</h3>
            <small>Color when hovering over buttons</small>
        </td>
        <td>
            <input type="color" 
                   name="<?= $this->FCRE->plugin_name ?>-button-hover-color" 
                   value="<?php echo esc_attr($button_hover_color); ?>" 
                   class="fcre-color-picker" 
                   id="button-hover-color">
            <input type="text" 
                   value="<?php echo esc_attr($button_hover_color); ?>" 
                   class="fcre-color-text" 
                   data-target="button-hover-color"
                   placeholder="#005a87">
        </td>
    </tr>
    
    <tr>
        <td colspan="2">
            <div class="fcre-color-preview">
                <h4>Preview</h4>
                <div class="preview-container">
                    <div class="preview-header" style="background-color: <?php echo esc_attr($primary_color); ?>;">
                        <h3 style="color: white; margin: 0; padding: 15px;">Sample Header</h3>
                    </div>
                    <div class="preview-content" style="padding: 20px; background: #f9f9f9;">
                        <p>This is how your colors will look on the frontend.</p>
                        <button class="preview-button" 
                                style="background-color: <?php echo esc_attr($button_color); ?>; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;"
                                onmouseover="this.style.backgroundColor='<?php echo esc_attr($button_hover_color); ?>'"
                                onmouseout="this.style.backgroundColor='<?php echo esc_attr($button_color); ?>'">
                            Sample Button
                        </button>
                        <div style="margin-top: 15px; padding: 10px; border-left: 4px solid <?php echo esc_attr($secondary_color); ?>; background: white;">
                            <strong>Secondary color accent</strong> - Used for highlights and accents
                        </div>
                    </div>
                </div>
            </div>
        </td>
    </tr>
</table>

<style>
.fcre-color-picker-table {
    width: 100%;
    border-collapse: collapse;
}

.fcre-color-picker-table td {
    padding: 15px;
    border-bottom: 1px solid #e1e1e1;
    vertical-align: top;
}

.fcre-color-picker {
    width: 60px;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.fcre-color-text {
    width: 100px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: monospace;
}

.fcre-color-preview {
    margin-top: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.preview-container {
    background: white;
}

.preview-header {
    transition: background-color 0.3s ease;
}

.preview-button {
    transition: background-color 0.3s ease;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Sync color picker with text input
    $('.fcre-color-picker').on('change', function() {
        var colorValue = $(this).val();
        var targetId = $(this).attr('id');
        $('input[data-target="' + targetId + '"]').val(colorValue);
        updatePreview();
    });
    
    // Sync text input with color picker
    $('.fcre-color-text').on('change keyup', function() {
        var colorValue = $(this).val();
        var targetId = $(this).data('target');
        if (isValidHexColor(colorValue)) {
            $('#' + targetId).val(colorValue);
            updatePreview();
        }
    });
    
    function isValidHexColor(hex) {
        return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
    }
    
    function updatePreview() {
        var primaryColor = $('#primary-color').val();
        var secondaryColor = $('#secondary-color').val();
        var buttonColor = $('#button-color').val();
        var buttonHoverColor = $('#button-hover-color').val();
        
        $('.preview-header').css('background-color', primaryColor);
        $('.preview-button').css('background-color', buttonColor);
        $('.preview-content div').css('border-left-color', secondaryColor);
        
        // Update button hover effect
        $('.preview-button').off('mouseenter mouseleave').on({
            mouseenter: function() {
                $(this).css('background-color', buttonHoverColor);
            },
            mouseleave: function() {
                $(this).css('background-color', buttonColor);
            }
        });
    }
});
</script>
