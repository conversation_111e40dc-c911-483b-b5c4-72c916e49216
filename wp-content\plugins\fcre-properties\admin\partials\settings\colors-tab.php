<?php
if (!defined('ABSPATH')) {
    die('-1');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Save color settings
    if (isset($_POST[$this->FCRE->plugin_name . '-primary-color'])) {
        update_option($this->FCRE->plugin_name . '-primary-color', sanitize_hex_color($_POST[$this->FCRE->plugin_name . '-primary-color']));
    }
    if (isset($_POST[$this->FCRE->plugin_name . '-secondary-color'])) {
        update_option($this->FCRE->plugin_name . '-secondary-color', sanitize_hex_color($_POST[$this->FCRE->plugin_name . '-secondary-color']));
    }
    if (isset($_POST[$this->FCRE->plugin_name . '-button-color'])) {
        update_option($this->FCRE->plugin_name . '-button-color', sanitize_hex_color($_POST[$this->FCRE->plugin_name . '-button-color']));
    }
    if (isset($_POST[$this->FCRE->plugin_name . '-button-hover-color'])) {
        update_option($this->FCRE->plugin_name . '-button-hover-color', sanitize_hex_color($_POST[$this->FCRE->plugin_name . '-button-hover-color']));
    }
    
    // Show success message
    echo '<div class="notice notice-success is-dismissible"><p>Color settings saved successfully!</p></div>';
}

// Get saved color values
$primary_color = get_option($this->FCRE->plugin_name . '-primary-color', '#6ea9dc');
$secondary_color = get_option($this->FCRE->plugin_name . '-secondary-color', '#005a87');
$button_color = get_option($this->FCRE->plugin_name . '-button-color', '#6ea9dc');
$button_hover_color = get_option($this->FCRE->plugin_name . '-button-hover-color', '#4b82b1');
?>

<div class="tab-content-header">
    <h2>Color Settings</h2>
    <p>Customize the colors for your property listings and frontend elements.</p>
</div>

<table class="fcre-color-picker-table">
    <tr>
        <td valign="top" width="25%">
            <h3>Primary Color</h3>
            <small>Main brand color used for primary elements</small>
        </td>
        <td>
            <input type="text"
                   name="<?= $this->FCRE->plugin_name ?>-primary-color"
                   value="<?php echo esc_attr($primary_color); ?>"
                   class="wp-color-picker"
                   data-default-color="#6ea9dc">
        </td>
    </tr>

    <tr>
        <td valign="top" width="25%">
            <h3>Secondary Color</h3>
            <small>Secondary brand color for accents and highlights</small>
        </td>
        <td>
            <input type="text"
                   name="<?= $this->FCRE->plugin_name ?>-secondary-color"
                   value="<?php echo esc_attr($secondary_color); ?>"
                   class="wp-color-picker"
                   data-default-color="#005a87">
        </td>
    </tr>

    <tr>
        <td valign="top" width="25%">
            <h3>Button Color</h3>
            <small>Default color for buttons and call-to-action elements</small>
        </td>
        <td>
            <input type="text"
                   name="<?= $this->FCRE->plugin_name ?>-button-color"
                   value="<?php echo esc_attr($button_color); ?>"
                   class="wp-color-picker"
                   data-default-color="#6ea9dc">
        </td>
    </tr>

    <tr>
        <td valign="top" width="25%">
            <h3>Button Hover Color</h3>
            <small>Color when hovering over buttons</small>
        </td>
        <td>
            <input type="text"
                   name="<?= $this->FCRE->plugin_name ?>-button-hover-color"
                   value="<?php echo esc_attr($button_hover_color); ?>"
                   class="wp-color-picker"
                   data-default-color="#4b82b1">
        </td>
    </tr>

</table>
<br>

<style>
.fcre-color-picker-table {
    width: 100%;
    border-collapse: collapse;
}

.fcre-color-picker-table td {
    padding: 15px 0;
    border-bottom: 1px solid #e1e1e1;
    vertical-align: top;
}

.fcre-color-picker-table h3 {
    margin-top: 0;
    margin-bottom: 5px;
}

.fcre-color-picker-table small {
    color: #666;
    font-style: italic;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Initialize WordPress color picker
    $('.wp-color-picker').wpColorPicker();
});
</script>
