(function ($) {
	'use strict';

	$(function () {
		var FILTER_LISTINGS = {
			init: function () {
				this.cacheDOM();
				this.bindEvents();
				this.filterProperties();
				this.tabsInit();
				this.customModel();
				this.submitAgreement();
				this.submitRequestMoreInfo();
				this.singleOverviewMap();
				this.checkSignedAgreement();
			},
			cacheDOM: function () {
				this.$filterWrapper = $('.fcre-filter-wrapper');
				this.$resultsWrapper = $('.fcre-results');
			},
			bindEvents: function () {
				this.$filterWrapper.on('change', 'input, select', this.filterProperties.bind(this));
				$('.fcre-multiselect-wrapper').each(function () {
					// We cannot use 'this' instead of FILTER_LISTINGS here because:
					// 1. 'this' inside the .each() function refers to the current DOM element, not the FILTER_LISTINGS object.
					// 2. We need to access the initFcreMultiselect method of the FILTER_LISTINGS object.
					// 3. Using FILTER_LISTINGS ensures we're calling the correct method from the correct object.
					FILTER_LISTINGS.initFcreMultiselect($(this));
				});

			},
			tabsInit: function () {

				const tabButtons = document.querySelectorAll(".fcre-tab-btn");
				const tabContents = document.querySelectorAll(".fcre-tab-content");

				tabButtons.forEach((btn) => {

					btn.addEventListener("click", () => {

						const target = btn.getAttribute("data-tab");

						tabButtons.forEach((b) => b.classList.remove("active"));
						tabContents.forEach((c) => c.classList.remove("active"));

						btn.classList.add("active");
						document.getElementById(target).classList.add("active");

						if (target === 'map-tab') {
							if (!window.mapInitialized) {
								window.mapInitialized = true; // Prevent reinitialization
								FILTER_LISTINGS.singleMainMap();
							}
						}

					});
				});
			},
			customModel: function () {
				const triggers = document.querySelectorAll('.custom-modal-trigger');
				const modals = document.querySelectorAll('.custom-modal');
				const closeBtns = document.querySelectorAll('.custom-modal-close');

				triggers.forEach(trigger => {
					trigger.addEventListener('click', function () {
						const targetId = this.getAttribute('data-modal-id');
						const modal = document.getElementById(targetId);
						if (modal) {
							modal.style.display = 'block';
							document.body.classList.add('modal-open');
						}
					});
				});

				closeBtns.forEach(btn => {
					btn.addEventListener('click', function () {
						const modal = this.closest('.custom-modal');
						modal.style.display = 'none';
						document.body.classList.remove('modal-open');
					});
				});

				window.addEventListener('click', function (e) {
					modals.forEach(modal => {
						if (e.target === modal) {
							modal.style.display = 'none';
							document.body.classList.remove('modal-open');
						}
					});
				});
			},
			initFcreMultiselect: function (wrapper) {
				var placeholder = wrapper.find('.filter-placeholder');
				var selectAll = wrapper.find('.fcre-select-all');
				var groupSelector = selectAll.data('group-selector');

				wrapper.find('.filter-onclick').on('click', function (e) {
					e.stopPropagation();
					var dropdown = $(this).siblings('.filter-dropdown');
					$('.filter-dropdown').not(dropdown).addClass('fcre-hide');
					dropdown.toggleClass('fcre-hide');
				});

				$(document).on('click', function (e) {
					if (!$(e.target).closest(".filter-select, .filter-dropdown").length) {
						$(".filter-dropdown").addClass("fcre-hide");
					}
				});

				selectAll.on('change', function () {
					wrapper.find(groupSelector).prop('checked', $(this).prop('checked'));
					updatePlaceholder();
				});

				wrapper.find(groupSelector).on('change', function () {
					selectAll.prop('checked', wrapper.find(groupSelector + ':checked').length === wrapper.find(groupSelector).length);
					updatePlaceholder();
				});

				function updatePlaceholder() {
					var selected = [];
					wrapper.find(groupSelector + ':checked').each(function () {
						selected.push($(this).data('name'));
					});

					if (selected.length === 0) {
						placeholder.text(wrapper.find('.filter-select').data('placeholder') || 'Select');
					} else if (selected.length === wrapper.find(groupSelector).length) {
						placeholder.text('All');
					} else {
						placeholder.text(selected.join(', '));
					}
				}

				updatePlaceholder();

			},
			collectFilters: function (wrapperSelector = '.fcre-filter-wrapper') {
				const formData = new FormData();
				const $form = jQuery(wrapperSelector);

				$form.find('input, select, textarea').each(function () {
					const $el = jQuery(this);
					const name = $el.attr('name');
					const type = $el.attr('type');
					if (!name) return;

					if (type === 'checkbox') {
						if ($el.is(':checked')) {
							formData.append(name + '[]', $el.val()); // append [] for checkboxes
						}
					} else if (type === 'radio') {
						if ($el.is(':checked')) {
							formData.append(name, $el.val());
						}
					} else {
						formData.append(name, $el.val());
					}
				});

				formData.append('action', 'fcre_filter_properties'); // WordPress AJAX action

				return formData;
			},

			filterProperties: function () {
				const data = FILTER_LISTINGS.collectFilters();

				$.ajax({
					url: FILTER_VAR.admin_ajax,
					type: 'POST',
					data: data,
					processData: false,     // must be false for FormData
					contentType: false,     // must be false for FormData
					beforeSend: function () {
						$(document).find('.fcre-results').html('Loading...');
					},
					success: function (response) {
						if (response.success && response.data.length > 0) {
							var html = '';
							response.data.forEach(function (item) {
								html += item.property_html;
							});
							$(document).find('.fcre-results').html(html);
							FILTER_LISTINGS.mainPropertiesMap(response.data);
						} else {
							$(document).find('.fcre-results').html('No properties found.');
						}
					}
				});
			},
			mainPropertiesMap: function (location) {

				var fcre_listing_map_container = document.getElementById('fcre-listing-map-container');
				if(fcre_listing_map_container === null) {
					return; // Exit if the container does not exist
				}
				fcre_listing_map_container.innerHTML = "<div id='map' style='width: 100%; height: 100%;'></div>";
				// map.invalidateSize();
				var locations = [];
				for (var i = 0; i < location.length; i++) {
					var LocationData = [location[i].latitude, location[i].longitude, location[i].property_html];
					locations.push(LocationData);
				}


				var markerIcon = L.icon({
					iconUrl: FILTER_VAR.marker_icon_url,
					iconSize: [64, 64], // size of the icon
					iconAnchor: [32, 32], // point of the icon which will correspond to marker's location
					popupAnchor: [0, 0] // point from which the popup should open relative to the iconAnchor
				});

				var map = L.map('map', {
					attributionControl: false,
					zoomControl: true,
					scrollWheelZoom: false,
					zoomAnimation: true,
					zoomSnap: 0.1,
					zoomDelta: 0.5,
					dragging: true,
					}).setView([34.88061495807371, -79.62412814353578], 8);

				L.tileLayer('http://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
					maxZoom: 20,
					subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
				}).addTo(map);

				map.panTo(new L.LatLng(34.88061495807371, -79.62412814353578));


				var markers = L.markerClusterGroup({
					// spiderfyOnMaxZoom: false, 
					showCoverageOnHover: false,
					// zoomToBoundsOnClick: false 
				});

				for (var i = 0; i < locations.length; i++) {
					var a = locations[i];
					var popupHtml = a[2];
					var marker = L.marker(new L.LatLng(a[0], a[1]), { icon: markerIcon });
					marker.bindPopup(popupHtml);
					markers.addLayer(marker);
				}

				map.addLayer(markers);

				map.getContainer().addEventListener('wheel', function (e) {
					if (e.ctrlKey) {
						e.preventDefault(); // ✅ prevent browser zoom
						map.scrollWheelZoom.enable();
					} else {
						map.scrollWheelZoom.disable();
					}
				}, { passive: false }); // ✅ Important to allow preventDefault()

			},
			singleMainMap: function () {

				var map_element = jQuery('#fcre-single-property-map');
				var lat = map_element.data('lat');
				var lng = map_element.data('lng');

				var map = L.map('fcre-single-property-map', { attributionControl: false }).setView([lat, lng], 8);

				L.tileLayer('http://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
					maxZoom: 14,
					subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
				}).addTo(map);


				var markerIcon = L.icon({
					iconUrl: FILTER_VAR.marker_icon_url,
					iconSize: [64, 64], // size of the icon
					iconAnchor: [32, 32], // point of the icon which will correspond to marker's location
					popupAnchor: [0, 0] // point from which the popup should open relative to the iconAnchor
				});

				var marker = L.marker([lat, lng], { icon: markerIcon });
				marker.bindPopup(map_element.data('address'));
				marker.addTo(map);

				// Ensure the map renders correctly
				setTimeout(function () {
					map.invalidateSize();
				}, 100);

			},
			singleOverviewMap: function () {

				var map_element = jQuery('#fcre-single-property-map-overview');
				var lat = map_element.data('lat');
				var lng = map_element.data('lng');

				var map = L.map('fcre-single-property-map-overview', { attributionControl: false }).setView([lat, lng], 8);

				L.tileLayer('http://{s}.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
					maxZoom: 14,
					subdomains: ['mt0', 'mt1', 'mt2', 'mt3']
				}).addTo(map);


				var markerIcon = L.icon({
					iconUrl: FILTER_VAR.marker_icon_url,
					iconSize: [64, 64], // size of the icon
					iconAnchor: [32, 32], // point of the icon which will correspond to marker's location
					popupAnchor: [0, 0] // point from which the popup should open relative to the iconAnchor
				});

				var marker = L.marker([lat, lng], { icon: markerIcon });
				marker.bindPopup(map_element.data('address'));
				marker.addTo(map);

				// Ensure the map renders correctly
				setTimeout(function () {
					map.invalidateSize();
				}, 100);

			},
			submitAgreement: function(){
				jQuery('#agreement-submit').on('click', function(event){
					event.preventDefault();
					event.stopPropagation();

					var data = new FormData($(this).closest('form')[0]);

					$(this).addClass('button-loading');

					jQuery.ajax({
						url: FILTER_VAR.admin_ajax,
						type: 'POST',
						data: data,
						processData: false,     // must be false for FormData
						contentType: false,     // must be false for FormData
						success: function(response){
							if(response.success){
								$(this).closest('form').find('input[type=text]').val('');
								$(this).closest('form').find('input[type=tel]').val('');
								$(this).closest('form').find('input[type=email]').val('');
								$(this).closest('form').find('textarea').val('');
								location.reload();
							}
							$(this).removeClass('button-loading');
						},
						error : function(response){
							$(this).removeClass('button-loading');
						}
					});
				});
			},
			checkSignedAgreement: function(){
				jQuery('#agreement-email').on('keyup', function(event){
					event.preventDefault();
					
					var email = $(this).val();
					var property_id = $(this).data('property-id');
					var messageElement = $(this).siblings('.agreement-message');
					
					var formData = new FormData();
					formData.append('action', 'fcre_check_signed_agreement');
					formData.append('email', email);
					formData.append('property_id', property_id);

					jQuery.ajax({
						url: FILTER_VAR.admin_ajax,
						type: 'POST',
						data: formData,
						processData: false,
						contentType: false,
						success: function(response){
							if(response.status){
								// messageElement.html('You have already signed the agreement for this property.');
								messageElement.show();
							} else {
								messageElement.hide();
							}
						},
						error: function(response){
							messageElement.hide();
						}
					});
				});
			},
			submitRequestMoreInfo: function(){
				jQuery('#request-more-info-form').on('submit', function(event){
					event.preventDefault();
					event.stopPropagation();

					var data = new FormData($(this)[0]);

					$(this).find('button[type=submit]').addClass('button-loading');

					jQuery.ajax({
						url: FILTER_VAR.admin_ajax,
						type: 'POST',
						data: data,
						processData: false,     // must be false for FormData
						contentType: false,     // must be false for FormData
						success: function(response){
							if(response.success){
								$(this).find('input[type=text]').val('');
								$(this).find('input[type=tel]').val('');
								$(this).find('input[type=email]').val('');
								$(this).find('textarea').val('');
							}
							$(this).find('button[type=submit]').removeClass('button-loading');
						},
						error : function(response){
							$(this).find('button[type=submit]').removeClass('button-loading');
						}
					});
				});
			}

			
		};

		FILTER_LISTINGS.init();

	});

}(jQuery));


