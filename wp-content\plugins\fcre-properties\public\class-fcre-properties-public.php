<?php

/**
 * The public-facing functionality of the plugin.
 *
 * @link       https://focusedcre.com
 * @since      1.0.0
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/public
 */

/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the public-facing stylesheet and JavaScript.
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/public
 * <AUTHOR> CRE <<EMAIL>>
 */
class Fcre_Properties_Public
{

	private $FCRE;
	private $plugin_name;
	private $version;

	public function __construct()
	{

		$this->FCRE = Fcre_Global::getInstance();
		$this->plugin_name = $this->FCRE->plugin_name;
		$this->version = $this->FCRE->version;
	}

	/**
	 * Register the stylesheets for the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_styles()
	{

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Fcre_Properties_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Fcre_Properties_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_style('leaflet-css', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css', array(), null, 'all');
		wp_enqueue_style('fotorama-css', 'https://cdnjs.cloudflare.com/ajax/libs/fotorama/4.6.4/fotorama.css', array(), null, 'all');
		wp_enqueue_style($this->plugin_name, plugin_dir_url(__FILE__) . 'css/fcre-properties-public.css', array(), $this->version, 'all');

	}

	/**
	 * Add custom CSS styles based on color settings
	 *
	 * @since    1.0.0
	 */
	public function add_custom_colors_css()
	{
		// Get saved color values
		$primary_color = get_option($this->plugin_name . '-primary-color', '#6ea9dc');
		$secondary_color = get_option($this->plugin_name . '-secondary-color', '#005a87');
		$button_color = get_option($this->plugin_name . '-button-color', '#6ea9dc');
		$button_hover_color = get_option($this->plugin_name . '-button-hover-color', '#4b82b1');

		// Generate custom CSS
		$custom_css = "
		<style type='text/css' id='fcre-custom-colors'>

		/* Primary color applications */
		.fcre-primary-bg{
			background-color: {$primary_color} !important;
		}

		.fcre-primary-text {
			color: {$primary_color} !important;
		}

		/* Secondary color applications */
		.fcre-secondary-bg {
			background-color: {$secondary_color} !important;
		}

		.fcre-secondary-text {
			color: {$secondary_color} !important;
		}

		.fcre-property-card:hover,
		.snap-cre-property-card:hover {
			border-color: {$secondary_color} !important;
		}
		.fcre-listing-image span{
			background: {$secondary_color} !important;
		}

		/* Button styling */
		.fcre-btn-primary,
		.fcre-btn,
		.fcre-filter-btn,
		.fcre-submit-btn,
		.filter-submit,
		button.fcre-btn-primary,
		input[type=submit].fcre-btn {
			background-color: {$button_color} !important;
			border-color: {$button_color} !important;
		}

		.fcre-btn-primary:hover,
		.fcre-btn:hover,
		.fcre-filter-btn:hover,
		.fcre-submit-btn:hover,
		.filter-submit:hover,
		button.fcre-btn-primary:hover,
		input[type=submit].fcre-btn:hover {
			background-color: {$button_hover_color} !important;
			border-color: {$button_hover_color} !important;
		}

		/* Links and accents */
		.fcre-property-link,
		.fcre-property-title a,
		.property-link,
		.snap-cre-property-card .property-link {
			color: {$primary_color} !important;
		}

		.fcre-property-link:hover,
		.fcre-property-title a:hover,
		.property-link:hover,
		.snap-cre-property-card .property-link:hover {
			color: {$button_hover_color} !important;
		}

		/* Form elements */
		.fcre-form-control:focus,
		.filter-field input:focus,
		.filter-field select:focus {
			border-color: {$primary_color} !important;
			box-shadow: 0 0 0 0.25rem rgba(" . $this->hex_to_rgb($primary_color) . ", 0.25) !important;
		}

		/* Tabs */
		.fcre-tab-btn.active,
		.fcre-tab-titles li.active {
			border-color: {$primary_color} !important;
			color: {$primary_color} !important;
		}
		.fcre-tab-btn.active, .fcre-tab-btn:hover{
			color: {$primary_color} !important;
			border-color: {$primary_color} !important;
		}

		/* Property status badges */
		.fcre-status-badge,
		.property-status {
			background-color: {$secondary_color} !important;
		}

		/* Map markers and popups */
		.leaflet-popup-content-wrapper {
			border-top: 3px solid {$primary_color} !important;
		}

		/* Filter wrapper styling */
		.snap-cre-filter-wrapper {
			border-left: 4px solid {$primary_color} !important;
		}

		/* Property grid cards */
		.snap-cre-property-card {
			border-bottom: 2px solid transparent !important;
			transition: border-color 0.3s ease !important;
		}

		.snap-cre-property-card:hover {
			border-bottom-color: {$primary_color} !important;
		}
		</style>";

		echo $custom_css;
	}

	/**
	 * Convert hex color to RGB values
	 *
	 * @param string $hex Hex color code
	 * @return string RGB values separated by commas
	 */
	private function hex_to_rgb($hex)
	{
		$hex = ltrim($hex, '#');

		if (strlen($hex) == 3) {
			$hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
		}

		$r = hexdec(substr($hex, 0, 2));
		$g = hexdec(substr($hex, 2, 2));
		$b = hexdec(substr($hex, 4, 2));

		return "$r, $g, $b";
	}

	/**
	 * Register the JavaScript for the public-facing side of the site.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_scripts()
	{

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Fcre_Properties_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Fcre_Properties_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_script('leaflet-js', 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js', array('jquery'), null, true);
		wp_enqueue_script('fotorama-js', 'https://cdnjs.cloudflare.com/ajax/libs/fotorama/4.6.4/fotorama.js', array('jquery'), null, true);
		wp_enqueue_script('leaflet-markercluster-js', 'https://unpkg.com/leaflet.markercluster@1.5.3/dist/leaflet.markercluster.js', array('jquery'), null, true);
		wp_enqueue_script('leaflet-markercluster-default-js', 'https://unpkg.com/leaflet.markercluster@1.5.3/dist/MarkerCluster.Default.js', array('jquery'), null, true);
		wp_enqueue_script($this->plugin_name.'-public', plugin_dir_url(__FILE__) . 'js/fcre-properties-public.js', array('jquery'), $this->version, false);
		
		$FILTER_VAR = array(
			'admin_ajax' => admin_url('admin-ajax.php'),
			'marker_icon_url' => plugin_dir_url(__FILE__) .'/img/map-marker.png', 
		);

		wp_localize_script($this->plugin_name.'-public', 'FILTER_VAR', $FILTER_VAR);
	}

	public function snapcre_filters($atts = [])
	{

		$property_types = isset($atts['property_types']) ? $atts['property_types'] : '';
		$transaction_types = isset($atts['transaction_types']) ? $atts['transaction_types'] : '';

		ob_start();

		fcre_get_template_part('filters/fcre', 'filters', array('property_types' => $property_types, 'transaction_types' => $transaction_types));

		fcre_get_template_part('filters/fcre', 'results');

		return ob_get_clean();
	}


	
    /**
     *  Set the template for single page
     *
     * @since    1.0.0
     */
    function fcre_single_post_template($single)
    {
        global $post;
        if ($post->post_type == $this->FCRE->properties_custom_post_slug) {
            $single = dirname(__FILE__) . '/templates/single-property.php';
        }
        if ($post->post_type == $this->FCRE->agent_custom_post_slug) {
            $single = dirname(__FILE__) . '/templates/single-agent.php';
        }
        return $single;
    }
}
