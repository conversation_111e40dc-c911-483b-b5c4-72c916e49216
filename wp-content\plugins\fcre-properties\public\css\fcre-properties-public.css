:root{
  --primary: #6ea9dc;
  --secondary: #f7f7f7;
  --btn-color: #ffffff;
  --btn-hover: #6ea9dc;
}
*{
  box-sizing: border-box;
}
.fcre-container-fluid{
  width: 100%;
  padding: 0 15px;
  margin: 0 auto; 
}
.fcre-container {
    width: 100%;
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 15px;
}
@media (max-width: 768px) {
  .fcre-container, .fcre-container-md, .fcre-container-sm {
      max-width: 720px;
  }
}

@media (max-width: 992px) {
  .fcre-container, .fcre-container-lg, .fcre-container-md, .fcre-container-sm {
      max-width: 960px;
  }
}

@media (min-width: 1400px) {
  .fcre-container, .fcre-container-lg, .fcre-container-md, .fcre-container-sm, .fcre-container-xl, .fcre-container-xxl {
      max-width: 1140px;
  }
}

.fcre-row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;  
    margin-right: -15px;
}

.fcre-row>* {
    flex-shrink: 0;
    width: 100%;
    max-width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}
.fcre-col {
    flex: 1;
    padding: 0 15px;
    min-width: 0;
}

.fcre-col-12 { flex: 0 0 auto; width: 100%; }
.fcre-col-11 { flex: 0 0 auto; width: 91.6666%; }
.fcre-col-10 { flex: 0 0 auto; width: 83.3333%; }
.fcre-col-9 { flex: 0 0 auto; width: 75%; }
.fcre-col-8 { flex: 0 0 auto; width: 66.6666%; }
.fcre-col-7 { flex: 0 0 auto; width: 58.3333%; }
.fcre-col-6 { flex: 0 0 auto; width: 50%; }
.fcre-col-5 { flex: 0 0 auto; width: 41.6666%; }
.fcre-col-4 { flex: 0 0 auto; width: 33.3333%; }
.fcre-col-3 { flex: 0 0 auto; width: 25%; }
.fcre-col-2 { flex: 0 0 auto; width: 16.6666%; }
.fcre-col-1 { flex: 0 0 auto; width: 8.3333%; }

/* Responsive Adjustments */
@media (max-width: 782px) {
    .fcre-col-12,
    .fcre-col-11,
    .fcre-col-10,
    .fcre-col-9,
    .fcre-col-8,
    .fcre-col-7,
    .fcre-col-6,
    .fcre-col-5,
    .fcre-col-4,
    .fcre-col-3,
    .fcre-col-2, 
    .fcre-col-1 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

.fcre-form-group {
    margin-bottom: 10px;
}

.fcre-form-group > input[type="text"],
.fcre-form-group > input[type="number"],
.fcre-form-group > input[type="email"],
.fcre-form-group > input[type="tel"],
.fcre-form-group > input[type="date"],
.fcre-form-group > input[type="time"],
.fcre-form-group > input[type="url"],
.fcre-form-group > input[type="week"],
.fcre-form-group > input[type="color"],
.fcre-form-group > input[type="password"],

.fcre-form-group > select,
.fcre-form-group > textarea {
    width: 100%;
    padding: 0.5rem 1rem;
    margin: 0;
    box-sizing: border-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
}

.fcre-form-group > label {
  font-size: 16px;
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

.fcre-range-input{
  display: flex;
  align-items: center;
  gap: 5px;
}
.fcre-range-input span {
    font-size: 14px;
    width: 14px;
    flex-shrink: 0;
    line-height: 14px;
}

.custom-tabs {
    font-family: Arial, sans-serif;
    width: 100%;
  }
  
  .fcre-tab-buttons {
    display: flex;
    border-bottom: 2px solid #ccc;
    justify-content: space-between !important;
  }
  
  .fcre-tab-btn {
    padding: 10px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.2s ease-in-out;
    color: #7d8896;
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent;
  }
  
  
  .fcre-tab-btn.active,
  .fcre-tab-btn:hover
   {
    color: #0e76bc;
    border-color: #0e76bc;
  }
  .fcre-tab-btn.disabled{
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
.fcre-single-property .fcre-tab-content{
    min-height: 64vh;
}
  .fcre-tab-content {
    display: none;
    padding: 15px;
    border-top: none;
  }
  
  .fcre-tab-content.active {
    display: block;
  }
  
.fcre-single-property-map-overview {
    height: 500px;
}
.fcre-single-property-map {
    height: 500px;
}
.fcre-filter-wrapper {
    background: #f7f7f7;
    padding: 15px 0;
}
.fcre-hide{display:none;}
.fcre-show{display:block;}

.fcre-btn-download-flyer{
    width: 100%;
}
.fcre-btn-agreement{
    width: 100%;
}

.agreement-message {
    border: 1px solid #FFC107;
    padding: 10px;
    background: #fff7e5;
    border-radius: 5px;
}
.agreement-message a {
    font-weight: bold;
}
.w-full{
    width: 100%;
}

.fcre-tab-tooltip {
   position: relative;
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s ease-in-out;
  color: #7d8896;
  background-color: transparent;
  border: none;
  border-bottom: 2px solid transparent;
}

.fcre-tab-tooltip .tooltip-text {
    visibility: hidden;
    width: max-content;
    background-color: #333333;
    color: #fff;
    text-align: center;
    padding: 6px 10px;
    font-size: 14px;
    border-radius: 5px;
    position: absolute;
    z-index: 1;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
    white-space: nowrap;
}

.fcre-tab-tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

.fcre-tab-tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%; /* Bottom of tooltip box */
  left: 50%;
  transform: translateX(-50%);
  border-width: 6px;
  border-style: solid;
  border-color: #333 transparent transparent transparent; /* Arrow pointing down */
}

.fcre-multiselect-wrapper{width:100%;position:relative;}
.fcre-multiselect-wrapper .fcre-filter-label{font-size: 16px;display: block;margin-bottom: 5px;font-weight: 600;}
.fcre-multiselect-wrapper .filter-select{position:relative;border:1px solid #ced4da;width:100%;background:#ffffff;padding:0.7rem 0.3rem;border-radius:4px;}
.fcre-multiselect-wrapper .filter-select-arrow{position:absolute;top:17px;right:5px;border-top:6px solid #a7a7a7;border-left:6px solid transparent;border-bottom:6px solid transparent;border-right:6px solid transparent;z-index:1;}
.fcre-multiselect-wrapper .filter-onclick{position:absolute;top:0;left:0;width:100%;background:transparent;cursor:pointer;z-index:1;height:40px;}
.fcre-multiselect-wrapper .filter-dropdown{background:#ffffff;position:absolute;width:100%;top:34px;left:0;z-index:3333;border:1px solid #a7a7a7;}
.fcre-multiselect-wrapper .filter-dropdown-area{padding:10px;}
.fcre-multiselect-wrapper .filter-placeholder{font-size:12px;display:block;width:100%;padding:0 10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}
.fcre-multiselect-wrapper ul.fcre-dropdown{margin: 0;padding: 0;}
.fcre-multiselect-wrapper .fcre-dropdown li{list-style:none;line-height: 18px;}
.fcre-multiselect-wrapper .filter-dropdown-area label{font-size: 14px;}
.fcre-multiselect-wrapper .filter-placeholder{display:block; width:100%; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;}



.leaflet-cluster-anim .leaflet-marker-icon,.leaflet-cluster-anim .leaflet-marker-shadow{-webkit-transition:-webkit-transform 0.3s ease-out,opacity 0.3s ease-in; -moz-transition:-moz-transform 0.3s ease-out,opacity 0.3s ease-in; -o-transition:-o-transform 0.3s ease-out,opacity 0.3s ease-in; transition:transform 0.3s ease-out,opacity 0.3s ease-in;}
.leaflet-cluster-spider-leg{
/* stroke-dashoffset (duration and function) should match with leaflet-marker-icon transform in order to track it exactly */

	-webkit-transition:-webkit-stroke-dashoffset 0.3s ease-out,-webkit-stroke-opacity 0.3s ease-in; -moz-transition:-moz-stroke-dashoffset 0.3s ease-out,-moz-stroke-opacity 0.3s ease-in; -o-transition:-o-stroke-dashoffset 0.3s ease-out,-o-stroke-opacity 0.3s ease-in; transition:stroke-dashoffset 0.3s ease-out,stroke-opacity 0.3s ease-in;}
.marker-cluster-small{background-color:rgba(181,226,140,0.6);}
.marker-cluster-small div{background-color:rgba(110,204,57,0.6);}
.marker-cluster-medium{background-color:rgba(241,211,87,0.6);}
.marker-cluster-medium div{background-color:rgba(240,194,12,0.6);}
.marker-cluster-large{background-color:rgba(253,156,115,0.6);}
.marker-cluster-large div{background-color:rgba(241,128,23,0.6);}
/* IE 6-8 fallback colors */
.leaflet-oldie .marker-cluster-small{background-color:rgb(181,226,140);}
.leaflet-oldie .marker-cluster-small div{background-color:rgb(110,204,57);}
.leaflet-oldie .marker-cluster-medium{background-color:rgb(241,211,87);}
.leaflet-oldie .marker-cluster-medium div{background-color:rgb(240,194,12);}
.leaflet-oldie .marker-cluster-large{background-color:rgb(253,156,115);}
.leaflet-oldie .marker-cluster-large div{background-color:rgb(241,128,23);}
.marker-cluster{background-clip:padding-box; border-radius:20px;}
.marker-cluster div{width:30px; height:30px; margin-left:5px; margin-top:5px; text-align:center; border-radius:15px; font:12px "Helvetica Neue",Arial,Helvetica,sans-serif;}
.marker-cluster span{line-height:30px;}

#fcre-listing-map-container{width: 100%; height:900px; border:1px solid #ccc;}

#fcre-listing-map-container .leaflet-popup-content {
    margin: -12px !important;
    width: 450px !important;
}
#fcre-listing-map-container .leaflet-popup-content p{
  margin: 0;
	font-size: 14px;
	line-height: 21px;
}

#progress{display:none; position:absolute; z-index:1000; left:400px; top:300px; width:200px; height:20px; margin-top:-20px; margin-left:-100px; background-color:#fff; background-color:rgba(255,255,255,0.7); border-radius:4px; padding:2px;}
#progress-bar{width:0; height:100%; background-color:#76A6FC; border-radius:4px;}

.fcre-results{
    height: 900px;
    max-height: 100%;
    overflow-y: scroll;
}
.fcre-listing-box{
      display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0;
    background-color: #ffffff;
    margin-top: 10px;
}
.fcre-listing-link{
  text-decoration: none;
}

.fcre-listing-image {
	flex: 1;
	position: relative;
}
.fcre-listing-image span{
  padding: 5px 5px;
	display: inline-block;
	font-size: 10px;
	text-transform: uppercase;
	color: #ffffff;
  background: #de0082;
	font-weight: 600;
	letter-spacing: 1px;
	position: absolute;
	top: 0;
	left: 0;}

.fcre-listing-content{flex:2}
.fcre-listing-box h2 {
    font-size: 16px;
    line-height: 18px;
    text-transform: uppercase;
    font-family: inherit;
}
.fcre-listing-content p {
    font-size: 15px;
    line-height: 20px;
}


  .fcre-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    border-radius: 0.25rem;
    cursor: pointer;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .fcre-btn-primary {
    background-color: #007bff;
    color: #fff;
  }

  .fcre-btn-primary:hover {
    background-color: #0069d9;
    color: #ffffff;
  }

  .fcre-btn-secondary {
    background-color: #6c757d;
    color: #fff;
  }

  .fcre-btn-secondary:hover {
    background-color: #5a6268;
  }

  .fcre-icon-btn i{
    margin-right: 0.5rem;
  }
  .fcre-form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
  }
  .fcre-form-control:focus {
    outline: none;
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13,110,253,.25);
  }
  .fcre-form-control::placeholder {
    color: #6c757d;
    opacity: 1;
  }
  .fcre-form-control:focus::placeholder {
    color: transparent;
  }
  .fcre-form-control:disabled {
    background-color: #e9ecef;
    opacity: 1;
  }


  .custom-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    padding-top: 100px;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
}

.custom-modal-content {
    background-color: #fff;
    margin: auto;
    padding: 20px;
    border-radius: 6px;
    width: 100%;
    max-width: 750px;
    position: relative;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    margin-bottom: 150px;
}

.custom-modal-close {
    color: #aaa;
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.custom-modal-close:hover {
    color: black;
}
/* Prevent body scroll when modal is active */
body.modal-open {
    overflow: hidden;
}

.fcre-photos-gallery{
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.fcre-single-property-title {
    margin: 30px 0;
}
.fcre-single-property-overview .fcre-single-property-overview-item {
    display: flex;
    justify-content: space-between;
    text-align: left;
    padding: 12px 10px;
    border-bottom: 1px solid #0e76bc;
}
.fcre-single-property-overview .fcre-single-property-overview-item-label{
    flex: 1;
    letter-spacing: 1px;
}
.fcre-single-property-overview .fcre-single-property-overview-item-value {
    color: #7d8896;
    flex: 1;
    letter-spacing: 1px;
}

.fcre-single-property-demographics-item h2,
.fcre-single-property-demographics-item p
{
    margin: 5px auto;
}


a.pdf,a.docx,a.doc,a.xlsx,a.xls,a.zip,a.jpg,a.jpeg,a.png,a.bmp,a.gif,a.webp,a.avif,a.svg {
    display: block;
    width: fit-content;
    cursor: pointer;
}

a.jpg,a.jpeg,a.png,a.bmp,a.gif, a.webp, a.avif, a.svg {
    background: url('../img/image.png') center left no-repeat;
    padding: 10px 0 10px 30px;
    font-size: 16px;
    text-decoration: none;
}

a.pdf {
    background: url('../img/pdf.png') 0 8px no-repeat;
    padding: 5px 0 5px 30px;
    font-size: 16px;
    text-decoration: none;
}

a.docx {
    background: url('../img/word.png') center left no-repeat;
    padding: 5px 0 5px 30px;
    font-size: 16px;
    text-decoration: none;
}

a.doc {
    background: url('../img/word.png') center left no-repeat;
    padding: 5px 0 5px 30px;
    font-size: 16px;
    text-decoration: none;
}

a.pptx {
    background: url('../img/pptx.png') center left no-repeat;
    padding: 5px 0 5px 30px;
    font-size: 16px;
    text-decoration: none;
}

a.xlsx {
    background: url('../img/excel.png') center left no-repeat;
    padding: 5px 0 5px 30px;
    font-size: 16px;
    text-decoration: none;
}

a.zip {
    background: url('../img/zip.png') center left no-repeat;
    padding: 5px 0 5px 30px;
    font-size: 16px;
    text-decoration: none;
}
a.mp4 {
    background: url('../img/video.png') center left no-repeat;
    padding: 5px 0 5px 30px;
    font-size: 16px;
    text-decoration: none;
}

.fcre-single-property-sidebar-agents{
    margin-top: 30px;
}
.fcre-agent-list{
    list-style: none;
    padding: 0;
    margin: 5px 0 30px 0;
}
.fcre-agent-item{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}
.fcre-agent-photo{
    flex: 0 0 50px;
    margin-right: 10px;
}
.fcre-agent-photo img{
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 50%;
}
.fcre-agent-info{
    flex: 1;
}
.fcre-agent-info h3{
    margin: 0;
    font-size: 16px;
}
.fcre-agent-info p{
    margin: 0;
    font-size: 14px;
}