<?php
/**
 * The admin-specific functionality of the plugin.
 *
 * @link       https://focusedcre.com
 * @since      1.0.0
 *
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/includes
 */

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since      1.0.0
 * @package    Fcre_Properties
 * @subpackage Fcre_Properties/includes
 * <AUTHOR> CRE <<EMAIL>>
 */
class Fcre_Global{   
    // Hold the class instance.
    private static $instance = null;
    public $plugin_name;
    public $version;
    public $properties_custom_post_slug;
    public $agent_custom_post_slug;
    public $agreements_custom_post_slug;
    public $contacts_custom_post_slug;
    public $download_request_custom_post_slug;
    public $document_vault_custom_post_slug;
    public $email_from_name;
    public $email_from_email;
    public $email_bcc;
    public $email_cc;
    public $ask_question_agent_flag;
    public $ask_question_custom_flag;
    public $ask_question_emails;

    private function __construct()
    {
        $this->plugin_name = 'fcre-properties';

        if (defined('BTS_PROPERTIES_VERSION')) {
            $this->version = FCRE_PROPERTIES_VERSION;
        } else {
            $this->version = '1.0.0';
        }
        $this->properties_custom_post_slug = 'properties';
        $this->agent_custom_post_slug = 'agents';
        $this->agreements_custom_post_slug = 'agreements';
        $this->contacts_custom_post_slug = 'contacts';
        $this->download_request_custom_post_slug = 'download-request';
        $this->document_vault_custom_post_slug = 'document-vault';

        $this->email_from_name = 'FCRE Properties';
        $this->email_from_email = '<EMAIL>';
        $this->email_bcc = '';
        $this->email_cc = '';

        
        $this->ask_question_agent_flag = get_option($this->plugin_name . '-ask-a-question-selected-agents-flag');
        $this->ask_question_custom_flag = get_option($this->plugin_name . '-ask-a-question-custom-emails-flag');
        $this->ask_question_emails = get_option($this->plugin_name . '-ask-a-question-emails');


    }


    // The object is created from within the class itself
    // only if the class has no instance.
    public static function getInstance()
    {
        if (self::$instance == null) {
            self::$instance = new Fcre_Global();
        }

        return self::$instance;
    }
}