<?php
if (!defined('ABSPATH')) {
    die('-1');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
 
 
        if ($_POST[$this->FCRE->plugin_name . '-ask-a-question-selected-agents-flag'] == '1')
            update_option($this->FCRE->plugin_name . '-ask-a-question-selected-agents-flag', $_POST[$this->FCRE->plugin_name . '-ask-a-question-selected-agents-flag']);
        else
            update_option($this->FCRE->plugin_name . '-ask-a-question-selected-agents-flag', '0');
        if ($_POST[$this->FCRE->plugin_name . '-ask-a-question-custom-emails-flag'] == '1')
            update_option($this->FCRE->plugin_name . '-ask-a-question-custom-emails-flag', $_POST[$this->FCRE->plugin_name . '-ask-a-question-custom-emails-flag']);
        else
            update_option($this->FCRE->plugin_name . '-ask-a-question-custom-emails-flag', '0');
        if (is_string($_POST[$this->FCRE->plugin_name . '-ask-a-question-emails']))
            update_option($this->FCRE->plugin_name . '-ask-a-question-emails', $_POST[$this->FCRE->plugin_name . '-ask-a-question-emails']);
     
       
  
}

$ask_question_agent_flag = get_option($this->FCRE->plugin_name . '-ask-a-question-selected-agents-flag');
$ask_question_custom_flag = get_option($this->FCRE->plugin_name . '-ask-a-question-custom-emails-flag');
$ask_question_emails = get_option($this->FCRE->plugin_name . '-ask-a-question-emails');
 
?>
<div class="tab-content-header">
    <h2>Admin Email</h2>
     
</div>
<table class="fcre-color-piker-table">
    
	<tr>
		<td colspan="2">
            <p>On this page you can choose if you want to receive emails from the front-end  forms. You can choose to receive emails from the selected agent on the property, or you can enter comma separated emails in the field below to receive the emails submitted through the form.</p>
        </td>
	</tr>
    <tr>
        <td valign="top" width="20%"><h3>Admin emails notification </h3></td>
        <td>
            <div id="custom-form-container" class="radioOptions">
                <div><input type="checkbox" name="<?= $this->FCRE->plugin_name ?>-ask-a-question-selected-agents-flag" value="1" id="fcre-selected-agents" <?php checked($ask_question_agent_flag, '1') ?>><label for="fcre-selected-agents">Selected agent on property</label></div>
                <div><input type="checkbox" name="<?= $this->FCRE->plugin_name ?>-ask-a-question-custom-emails-flag" value="1" id="fcre-custom-emails" <?php checked($ask_question_custom_flag, '1') ?> ><label for="fcre-custom-emails">Enter comma separated emails bellow</label></d>
                <div><input type="text" name="<?= $this->FCRE->plugin_name ?>-ask-a-question-emails" class="fcre-properties-ask-a-question-emails" value="<?php echo $ask_question_emails; ?>"></d>
            </div>


        </td>
    </tr>

</table>

