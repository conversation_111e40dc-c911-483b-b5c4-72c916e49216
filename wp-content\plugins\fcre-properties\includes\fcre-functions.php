<?php
if (!function_exists('getIPAddress')) {
    function getIPAddress()
    {
        //whether ip is from the share internet
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } //whether ip is from the proxy
        elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } //whether ip is from the remote address
        else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        if ($ip == '::1') {
            $ip = '127.0.0.1';
        }
        return $ip;
    }
}

if(!function_exists('fcre_get_option_label_from_meta')) {
function fcre_get_option_label_from_meta($post_id, $meta_key, $option_key)
{
    $meta_value = get_post_meta($post_id, $meta_key, true);
    $options = get_option($option_key);

    if (empty($options) || !is_array($options)) {
        return '';
    }

    $selected_ids = is_array($meta_value) ? $meta_value : (array) $meta_value;

    $labels = [];

    foreach ($options as $option) {
        if (isset($option['id'], $option['name']) && in_array($option['id'], $selected_ids)) {
            $labels[] = $option['name'];
        }
    }

    return implode(', ', $labels);
}
}


if(!function_exists('is_from_current_website')) {
    function is_from_current_website()
    {
        $ref = (isset($_SERVER['HTTP_REFERER'])) ? $_SERVER['HTTP_REFERER'] : '';
        $refData = parse_url($ref);

        $refDomain = (isset($refData['host'])) ? $refData['host'] : '';

        $urlparts = parse_url(home_url());
        $domain = $urlparts['host'];

        if ($refDomain == $domain) {
            return true;
        } else {
            return false;
        }
    }
}


/**
 * The below function will help to load template file from plugin directory of wordpress
 *  Extracted from : http://wordpress.stackexchange.com/questions/94343/get-template-part-from-plugin
 */
function fcre_get_template_part($slug, $name = null, $args = null)
{

    do_action("fcre_get_template_part_{$slug}", $slug, $name, $args);

    $templates = array();
    if (isset($name))
        $templates[] = "{$slug}-{$name}.php";

    $templates[] = "{$slug}.php";

    fcre_get_template_path($templates, true, false, $args);
}

/* Extend locate_template from WP Core
* Define a location of your plugin file dir to a constant in this case = PLUGIN_DIR_PATH
* Note: PLUGIN_DIR_PATH - can be any folder/subdirectory within your plugin files
*/

function fcre_get_template_path($template_names, $load = false, $require_once = true, $args)
{
    $located = '';
    foreach ((array) $template_names as $template_name) {
        if (!$template_name)
            continue;

        /* search file within the PLUGIN_DIR_PATH only */
        if (file_exists(plugin_dir_path(__DIR__) . '/public/template-parts/' . $template_name)) {
            $located = plugin_dir_path(__DIR__) . '/public/template-parts/' . $template_name;
            break;
        }
    }

    if ($load && '' != $located) {
        load_template($located, $require_once, $args);
    }

    return $located;
}


function snapcre_folder_path()
{
    $upload = wp_upload_dir();
    $upload_dir = $upload['basedir'];
    $upload_dir = $upload_dir . '/fcre-properties';
    return $upload_dir;
}

 function snapcre_folder_url()
 {
     $upload = wp_upload_dir();
     $upload_dir = $upload['baseurl'];
     $upload_dir = $upload_dir . '/fcre-properties';
     return $upload_dir;
 }

function fcre_send_email($to, $subject, $message, $attachment = '')
{
    $FCRE = Fcre_global::getInstance();
    $headers = array('Content-Type: text/html; charset=UTF-8');
    $headers[] = 'From: ' . $FCRE->email_from_name . ' <' . $FCRE->email_from_email . '>';
    $headers[] = 'Reply-To: ' . $FCRE->email_from_name . ' <' . $FCRE->email_from_email . '>';
    $headers[] = 'Bcc: ' . $FCRE->email_bcc;
    $headers[] = 'Cc: ' . $FCRE->email_cc;
    $headers[] = 'X-Mailer: PHP/' . phpversion();
    $headers[] = 'X-Priority: 1';
    $headers[] = 'X-MSMail-Priority: High';
    $headers[] = 'Importance: High';
    $headers[] = 'MIME-Version: 1.0';
    $headers[] = 'Content-Type: text/html; charset=UTF-8';
    $headers[] = 'Content-Transfer-Encoding: 8bit';
    $headers[] = 'Date: ' . date('r', $_SERVER['REQUEST_TIME']);
    $headers[] = 'X-MS-Has-Attach: ' . (!empty($attachment) ? 'yes' : 'no');
    $headers[] = 'X-MS-TNEF-Correlator: ';

    $mail = wp_mail($to, $subject, $message, $headers, $attachment);
    return $mail;
}


function fcre_frontend_multiselect($args = []) {
    $defaults = [
        'option_key'  => '',
        'field_name'  => '',
        'label'       => '',
        'placeholder' => 'Select',
        'group_class' => '',
    ];
    $args = wp_parse_args($args, $defaults);

    if (empty($args['option_key']) || empty($args['field_name'])) {
        echo 'Missing required parameters.';
        return;
    }

    $options = get_option($args['option_key']);
    ?>
    <div class="fcre-multiselect-wrapper <?php echo esc_attr($args['group_class']); ?>">
        <?php if (!empty($args['label'])): ?>
            <label class="fcre-filter-label"><?php echo esc_html($args['label']); ?></label>
        <?php endif; ?>

        <div class="filter-select" data-placeholder="<?php echo esc_attr($args['placeholder']); ?>">
            <span class="filter-placeholder"></span>
            <span class="filter-select-arrow"></span>
            <span class="filter-onclick"></span>

            <div class="filter-dropdown fcre-hide">
                <div class="filter-dropdown-area">
                    <input type="checkbox" class="fcre-select-all" data-group-selector=".fcre-<?php echo esc_attr($args['field_name']); ?>-items" id="fcre-<?php echo esc_attr($args['field_name']); ?>-all">
                    <label for="fcre-<?php echo esc_attr($args['field_name']); ?>-all" class="text-black">All</label>
                    <ul class="fcre-dropdown">
                        <?php if (!empty($options) && is_array($options)): ?>
                            <?php foreach ($options as $index => $option): ?>
                                <li>
                                    <input
                                        type="checkbox"
                                        class="fcre-<?php echo esc_attr($args['field_name']); ?>-items"
                                        data-name="<?php echo esc_attr($option['name']); ?>"
                                        name="<?php echo esc_attr($args['field_name']); ?>"
                                        id="fcre-<?php echo esc_attr($args['field_name']); ?>-<?php echo $index; ?>"
                                        value="<?php echo esc_attr($option['id']); ?>"
                                    >
                                    <label for="fcre-<?php echo esc_attr($args['field_name']); ?>-<?php echo $index; ?>" class="text-black">
                                        <?php echo esc_html($option['name']); ?>
                                    </label>
                                </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <?php
}

function fcre_get_agent_emails($post_id)
{
    $FCRE = Fcre_global::getInstance();

    $related_agents = get_post_meta($post_id, 'related_agents', true);
    $related_agents = array_filter(explode(',', $related_agents));

    $agentEmailsArray = array();
    $agentEmails = '';

    if($related_agents){
        foreach($related_agents as $agent_id){
            $email = get_post_meta($agent_id, 'email', true);
            if ($email) {
                array_push($agentEmailsArray, $email);
            }
        }
        $agentEmails = implode(',', $agentEmailsArray);
    }


    if ($FCRE->ask_question_custom_flag == 1 && $FCRE->ask_question_agent_flag == 1) {
        $agentEmails = $agentEmails . ',' . $FCRE->ask_question_emails;
    } elseif ($FCRE->ask_question_custom_flag == 1) {
        $agentEmails = $FCRE->ask_question_emails;
    } elseif ($FCRE->ask_question_agent_flag == 1) {
        $agentEmails = $agentEmails;
    }
    return $agentEmails;
}